#!/bin/bash

# Shadowsocks 一鍵部署腳本
# 自動執行完整的部署流程

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 檢查必要文件
check_files() {
    log_step "檢查必要文件..."
    
    local files=(
        "ssh-setup.sh"
        "deploy-shadowsocks.sh"
        "configure-services.sh"
        "create-systemd-services.sh"
    )
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少文件: $file"
            exit 1
        fi
    done
    
    log_info "所有必要文件已存在"
}

# 設置權限
set_permissions() {
    log_step "設置腳本權限..."
    chmod +x ssh-setup.sh
    chmod +x deploy-shadowsocks.sh
    chmod +x configure-services.sh
    chmod +x create-systemd-services.sh
    log_info "權限設置完成"
}

# 執行部署步驟
deploy_step() {
    local step_name="$1"
    local script_name="$2"
    
    log_step "執行: $step_name"
    echo "========================================"
    
    if ./"$script_name"; then
        log_info "$step_name 完成"
    else
        log_error "$step_name 失敗"
        exit 1
    fi
    
    echo ""
    read -p "按 Enter 繼續下一步，或 Ctrl+C 退出..."
    echo ""
}

# 啟動服務
start_services() {
    log_step "啟動 Shadowsocks 服務..."
    
    ssh sg-vps "ss-manager.sh start"
    
    log_info "等待服務啟動..."
    sleep 5
    
    log_step "檢查服務狀態..."
    ssh sg-vps "ss-manager.sh status"
}

# 測試連接
test_connections() {
    log_step "測試 Shadowsocks 連接..."
    
    echo "測試端口連通性..."
    
    local ports=(28890 30004 30005 30006)
    local vps_ip="***********"
    
    for port in "${ports[@]}"; do
        if timeout 5 bash -c "</dev/tcp/$vps_ip/$port"; then
            log_info "端口 $port: 連通"
        else
            log_warn "端口 $port: 無法連接"
        fi
    done
}

# 顯示配置信息
show_config_info() {
    log_step "顯示配置信息..."
    
    echo ""
    echo "=== Shadowsocks 服務配置 ==="
    echo "服務器 IP: ***********"
    echo ""
    echo "管理員服務 (SG-AD-SS):"
    echo "  端口: 28890"
    echo "  密碼: K9mP2vX8nQ4wR7tY"
    echo "  加密: aes-256-gcm"
    echo ""
    echo "客戶端服務 1 (SG-CS-SS):"
    echo "  端口: 30004"
    echo "  密碼: L5nQ8xB3mV6zC9fG"
    echo "  加密: aes-256-gcm"
    echo ""
    echo "備用服務 (SG-RS-SS):"
    echo "  端口: 30005"
    echo "  密碼: M7pR4yD9nK2sF8hJ"
    echo "  加密: aes-256-gcm"
    echo ""
    echo "可選服務 (SG-OP-SS):"
    echo "  端口: 30006"
    echo "  密碼: N3tW6vE5mL8qA1xZ"
    echo "  加密: aes-256-gcm"
    echo ""
    echo "=== 管理命令 ==="
    echo "啟動服務: ssh sg-vps ss-manager.sh start"
    echo "停止服務: ssh sg-vps ss-manager.sh stop"
    echo "重啟服務: ssh sg-vps ss-manager.sh restart"
    echo "查看狀態: ssh sg-vps ss-manager.sh status"
    echo "查看日誌: ssh sg-vps ss-manager.sh logs [admin|client1|reserve|optional]"
    echo ""
    echo "=== Clash 配置 ==="
    echo "已更新的 Clash 配置文件: clash-config-chatgpt-fixed.yaml"
    echo "新增的代理組: Singapore"
    echo ""
}

# 主函數
main() {
    log_info "開始 Shadowsocks 一鍵部署..."
    echo ""
    
    check_files
    set_permissions
    
    echo "=== 部署流程 ==="
    echo "1. SSH 密鑰設置"
    echo "2. 安裝 go-shadowsocks2"
    echo "3. 配置服務"
    echo "4. 創建 systemd 服務"
    echo "5. 啟動服務"
    echo "6. 測試連接"
    echo ""
    
    read -p "是否開始部署？ (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    deploy_step "SSH 密鑰設置" "ssh-setup.sh"
    deploy_step "安裝 go-shadowsocks2" "deploy-shadowsocks.sh"
    deploy_step "配置服務" "configure-services.sh"
    deploy_step "創建 systemd 服務" "create-systemd-services.sh"
    
    start_services
    test_connections
    show_config_info
    
    log_info "Shadowsocks 部署完成！"
}

main "$@"
