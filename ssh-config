# SSH Configuration for VPS Deployment
# Add this to your ~/.ssh/config file

Host sg-vps
    HostName ***********
    User root
    Port 22
    IdentityFile ~/.ssh/id_rsa
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    LogLevel ERROR

# Alternative configuration with custom key
Host sg-vps-custom
    HostName ***********
    User root
    Port 22
    IdentityFile ~/.ssh/sg_vps_key
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    StrictHostKeyChecking ask
    
# For non-root user (recommended for security)
Host sg-vps-user
    HostName ***********
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/id_rsa
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    StrictHostKeyChecking ask
