{"services": [{"name": "SG-AD-SS", "description": "Singapore Admin Shadowsocks Service", "port": 28890, "password": "K9mP2vX8nQ4wR7tY", "method": "aes-256-gcm", "timeout": 300, "udp": true, "fast_open": true, "service_name": "shadowsocks-admin"}, {"name": "SG-CS-SS", "description": "Singapore Client Shadowsocks Service 1", "port": 30004, "password": "L5nQ8xB3mV6zC9fG", "method": "aes-256-gcm", "timeout": 300, "udp": true, "fast_open": true, "service_name": "shadowsocks-client1"}, {"name": "SG-RS-SS", "description": "Singapore Reserve Shadowsocks Service", "port": 30005, "password": "M7pR4yD9nK2sF8hJ", "method": "aes-256-gcm", "timeout": 300, "udp": true, "fast_open": true, "service_name": "shadowsocks-reserve"}, {"name": "SG-OP-SS", "description": "Singapore Optional Shadowsocks Service", "port": 30006, "password": "N3tW6vE5mL8qA1xZ", "method": "aes-256-gcm", "timeout": 300, "udp": true, "fast_open": true, "service_name": "shadowsocks-optional"}], "global_settings": {"server": "0.0.0.0", "dns": "*******,*******", "plugin": "", "plugin_opts": "", "workers": 1}}