# Clash Configuration Setup Guide

## 📋 Overview
This Clash configuration connects to your Shadowsocks VPS server at `*************` with 4 different proxy groups for load balancing and failover.

## 🔧 Server Information
- **Server IP**: *************
- **Server Host**: srv461468751.host
- **Encryption**: ChaCha20-IETF-Poly1305
- **Ports**: 8388, 8389, 8390, 8391

## 📁 Configuration Files
- `clash-config.yaml` - Main Clash configuration file
- `clash-setup-guide.md` - This setup guide

## 🚀 Quick Setup

### 1. Install Clash
**Windows:**
- Download Clash for Windows from official releases
- Extract and run `Clash for Windows.exe`

**macOS:**
- Download ClashX from App Store or GitHub
- Install and launch ClashX

**Linux:**
- Download clash binary for your architecture
- Make it executable: `chmod +x clash`

### 2. Import Configuration
1. Copy the `clash-config.yaml` file
2. In Clash client:
   - **Clash for Windows**: Profiles → Import → Select file
   - **ClashX**: Config → Import Config File
   - **Command Line**: `clash -f clash-config.yaml`

### 3. Start Proxy
1. Enable "System Proxy" in your Clash client
2. Select proxy mode (Rule/Global/Direct)
3. Choose proxy group from the interface

## 🎯 Proxy Groups Explained

### Primary Groups
- **Proxy**: Main proxy selector with all options
- **Auto-Select**: Automatically chooses fastest server
- **Fallback**: Switches to backup if primary fails
- **Load-Balance**: Distributes traffic across all servers
- **Streaming**: Optimized for streaming services

### Individual Servers
- **SS-Group1**: Port 8388 - Primary server
- **SS-Group2**: Port 8389 - Secondary server  
- **SS-Group3**: Port 8390 - Tertiary server
- **SS-Group4**: Port 8391 - Quaternary server

## 📊 Connection Details

| Group | Port | Password | Status |
|-------|------|----------|--------|
| Group1 | 8388 | Group1Pass2025 | ✅ Active |
| Group2 | 8389 | Group2Pass2025 | ✅ Active |
| Group3 | 8390 | Group3Pass2025 | ✅ Active |
| Group4 | 8391 | Group4Pass2025 | ✅ Active |

## 🔍 Testing Connection

### Method 1: Clash Dashboard
1. Open http://127.0.0.1:9090/ui in browser
2. Check proxy status and latency
3. Test individual connections

### Method 2: Command Line
```bash
# Test HTTP proxy
curl -x http://127.0.0.1:7890 http://httpbin.org/ip

# Test SOCKS5 proxy  
curl --socks5 127.0.0.1:7891 http://httpbin.org/ip
```

### Method 3: Browser
1. Enable system proxy in Clash
2. Visit https://whatismyipaddress.com
3. Verify IP shows: *************

## ⚙️ Configuration Features

### DNS Settings
- **Enhanced Mode**: Fake-IP for better performance
- **Primary DNS**: Google (*******, *******)
- **Fallback DNS**: Cloudflare (*******, *******)
- **Secure DNS**: DoT and DoH support

### Traffic Rules
- **Local Traffic**: Direct connection
- **Ad Blocking**: Blocks common ad domains
- **Streaming**: Optimized routing for media
- **China Sites**: Direct connection for CN domains
- **Global Sites**: Proxy for international services

### Ports
- **HTTP Proxy**: 7890
- **SOCKS5 Proxy**: 7891
- **External Controller**: 9090 (Dashboard)

## 🛠️ Troubleshooting

### Connection Issues
1. **Check server status**:
   ```bash
   ping *************
   telnet 38.89.************
   ```

2. **Verify passwords**: Ensure passwords match server config

3. **Test individual proxies**: Try each SS-Group separately

### Performance Issues
1. **Use Auto-Select**: Let Clash choose fastest server
2. **Check latency**: Monitor ping times in dashboard
3. **Switch groups**: Try Load-Balance for better distribution

### DNS Issues
1. **Clear DNS cache**: Restart Clash client
2. **Change DNS servers**: Modify nameserver settings
3. **Disable IPv6**: Set `ipv6: false` in DNS config

## 📱 Mobile Setup

### Android (Clash for Android)
1. Install from Google Play or GitHub
2. Import profile via QR code or file
3. Enable VPN permission
4. Start service

### iOS (Shadowrocket/Quantumult X)
1. Convert config to compatible format
2. Import subscription or manual config
3. Enable VPN in iOS settings
4. Connect through app

## 🔒 Security Notes

- All connections use ChaCha20-IETF-Poly1305 encryption
- Passwords are unique for each port
- UDP support enabled for better performance
- Local traffic bypasses proxy for security

## 📈 Performance Tips

1. **Use Auto-Select** for automatic optimization
2. **Enable Load-Balance** for high bandwidth usage
3. **Monitor latency** regularly through dashboard
4. **Update rules** periodically for better routing

## 🆘 Support

If you encounter issues:
1. Check server status with provided monitoring tools
2. Verify network connectivity to *************
3. Test individual proxy groups
4. Review Clash logs for error messages

---
*Configuration generated for VPS: srv461468751.host (*************)*
*Last updated: 2025-07-09*
