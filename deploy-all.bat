@echo off
REM Shadowsocks 一鍵部署腳本 (Windows 版本)
REM 自動執行完整的部署流程

setlocal enabledelayedexpansion

echo ========================================
echo    Shadowsocks 自動部署腳本
echo ========================================
echo.

REM 檢查必要文件
echo [檢查] 檢查必要文件...
set "missing_files="
if not exist "ssh-setup.sh" set "missing_files=!missing_files! ssh-setup.sh"
if not exist "deploy-shadowsocks.sh" set "missing_files=!missing_files! deploy-shadowsocks.sh"
if not exist "configure-services.sh" set "missing_files=!missing_files! configure-services.sh"
if not exist "create-systemd-services.sh" set "missing_files=!missing_files! create-systemd-services.sh"

if not "!missing_files!"=="" (
    echo [錯誤] 缺少文件: !missing_files!
    pause
    exit /b 1
)

echo [成功] 所有必要文件已存在
echo.

REM 顯示部署信息
echo === 部署信息 ===
echo VPS IP: ***********
echo 服務類型: go-shadowsocks2
echo 服務數量: 4 個 SS 實例
echo.
echo === 服務配置 ===
echo SG-AD-SS: 端口 28890 (管理員)
echo SG-CS-SS: 端口 30004 (客戶端 1)
echo SG-RS-SS: 端口 30005 (備用)
echo SG-OP-SS: 端口 30006 (可選)
echo.

REM 確認部署
set /p "confirm=是否開始部署？ (y/n): "
if /i not "%confirm%"=="y" (
    echo 部署已取消
    pause
    exit /b 0
)

echo.
echo === 開始部署流程 ===

REM 步驟 1: SSH 設置
echo.
echo [步驟 1/5] SSH 密鑰設置
echo ========================================
bash ssh-setup.sh
if errorlevel 1 (
    echo [錯誤] SSH 設置失敗
    pause
    exit /b 1
)
echo [成功] SSH 設置完成
pause

REM 步驟 2: 安裝 go-shadowsocks2
echo.
echo [步驟 2/5] 安裝 go-shadowsocks2
echo ========================================
bash deploy-shadowsocks.sh
if errorlevel 1 (
    echo [錯誤] go-shadowsocks2 安裝失敗
    pause
    exit /b 1
)
echo [成功] go-shadowsocks2 安裝完成
pause

REM 步驟 3: 配置服務
echo.
echo [步驟 3/5] 配置服務
echo ========================================
bash configure-services.sh
if errorlevel 1 (
    echo [錯誤] 服務配置失敗
    pause
    exit /b 1
)
echo [成功] 服務配置完成
pause

REM 步驟 4: 創建 systemd 服務
echo.
echo [步驟 4/5] 創建 systemd 服務
echo ========================================
bash create-systemd-services.sh
if errorlevel 1 (
    echo [錯誤] systemd 服務創建失敗
    pause
    exit /b 1
)
echo [成功] systemd 服務創建完成
pause

REM 步驟 5: 啟動服務
echo.
echo [步驟 5/5] 啟動服務
echo ========================================
bash start-services.sh
if errorlevel 1 (
    echo [錯誤] 服務啟動失敗
    pause
    exit /b 1
)
echo [成功] 服務啟動完成

echo.
echo ========================================
echo          部署完成！
echo ========================================
echo.
echo === 服務信息 ===
echo 服務器 IP: ***********
echo.
echo 管理員服務 (SG-AD-SS):
echo   端口: 28890
echo   密碼: K9mP2vX8nQ4wR7tY
echo   加密: aes-256-gcm
echo.
echo 客戶端服務 1 (SG-CS-SS):
echo   端口: 30004
echo   密碼: L5nQ8xB3mV6zC9fG
echo   加密: aes-256-gcm
echo.
echo 備用服務 (SG-RS-SS):
echo   端口: 30005
echo   密碼: M7pR4yD9nK2sF8hJ
echo   加密: aes-256-gcm
echo.
echo 可選服務 (SG-OP-SS):
echo   端口: 30006
echo   密碼: N3tW6vE5mL8qA1xZ
echo   加密: aes-256-gcm
echo.
echo === 管理命令 ===
echo 查看狀態: ssh sg-vps ss-manager.sh status
echo 重啟服務: ssh sg-vps ss-manager.sh restart
echo 查看日誌: ssh sg-vps ss-manager.sh logs admin
echo.
echo === Clash 配置 ===
echo 已更新: clash-config-chatgpt-fixed.yaml
echo 新增代理組: Singapore
echo.

pause
