#!/bin/bash

# Shadowsocks 自動部署腳本
# 使用 go-shadowsocks2 在 VPS 上部署多個 SS 服務

set -e

# 配置變量
VPS_HOST="sg-vps"
INSTALL_DIR="/opt/shadowsocks"
CONFIG_DIR="/etc/shadowsocks"
LOG_DIR="/var/log/shadowsocks"
SERVICE_USER="shadowsocks"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 檢查 SSH 連接
check_ssh_connection() {
    log_step "檢查 SSH 連接..."
    if ssh -o ConnectTimeout=10 "$VPS_HOST" "echo 'SSH 連接正常'" > /dev/null 2>&1; then
        log_info "SSH 連接成功"
    else
        log_error "SSH 連接失敗，請檢查 SSH 配置"
        exit 1
    fi
}

# 在遠程服務器上執行命令
remote_exec() {
    ssh "$VPS_HOST" "$1"
}

# 複製文件到遠程服務器
remote_copy() {
    scp "$1" "$VPS_HOST:$2"
}

# 安裝依賴
install_dependencies() {
    log_step "安裝系統依賴..."
    remote_exec "
        apt-get update
        apt-get install -y wget curl unzip systemd
        
        # 創建服務用戶
        if ! id '$SERVICE_USER' &>/dev/null; then
            useradd -r -s /bin/false '$SERVICE_USER'
            log_info '創建服務用戶: $SERVICE_USER'
        fi
        
        # 創建目錄
        mkdir -p '$INSTALL_DIR' '$CONFIG_DIR' '$LOG_DIR'
        chown '$SERVICE_USER:$SERVICE_USER' '$LOG_DIR'
    "
}

# 下載和安裝 go-shadowsocks2
install_go_shadowsocks2() {
    log_step "下載和安裝 go-shadowsocks2..."
    remote_exec "
        cd '$INSTALL_DIR'
        
        # 檢測系統架構
        ARCH=\$(uname -m)
        case \$ARCH in
            x86_64) ARCH='linux-amd64' ;;
            aarch64) ARCH='linux-arm64' ;;
            armv7l) ARCH='linux-arm' ;;
            *) echo '不支持的架構: \$ARCH'; exit 1 ;;
        esac
        
        # 下載最新版本
        LATEST_URL=\$(curl -s https://api.github.com/repos/shadowsocks/go-shadowsocks2/releases/latest | grep 'browser_download_url.*\$ARCH' | cut -d'\"' -f4)
        
        if [ -z '\$LATEST_URL' ]; then
            log_error '無法獲取下載鏈接'
            exit 1
        fi
        
        log_info '下載 go-shadowsocks2...'
        wget -O shadowsocks2.gz '\$LATEST_URL'
        gunzip shadowsocks2.gz
        chmod +x shadowsocks2
        
        # 創建符號鏈接
        ln -sf '$INSTALL_DIR/shadowsocks2' /usr/local/bin/shadowsocks2
        
        log_info 'go-shadowsocks2 安裝完成'
    "
}

# 配置防火牆
configure_firewall() {
    log_step "配置防火牆..."
    remote_exec "
        # 檢查防火牆狀態
        if command -v ufw >/dev/null 2>&1; then
            # 開放 SS 端口
            ufw allow 28890/tcp comment 'SS Admin'
            ufw allow 30004/tcp comment 'SS Client 1'
            ufw allow 30005/tcp comment 'SS Client 2'
            ufw allow 30006/tcp comment 'SS Client 3'
            
            # 開放 UDP（如果需要）
            ufw allow 28890/udp comment 'SS Admin UDP'
            ufw allow 30004/udp comment 'SS Client 1 UDP'
            ufw allow 30005/udp comment 'SS Client 2 UDP'
            ufw allow 30006/udp comment 'SS Client 3 UDP'
            
            log_info '防火牆規則已配置'
        else
            log_warn 'UFW 未安裝，請手動配置防火牆'
        fi
    "
}

# 主部署函數
main() {
    log_info "開始部署 Shadowsocks 服務..."
    
    check_ssh_connection
    install_dependencies
    install_go_shadowsocks2
    configure_firewall
    
    log_info "基礎部署完成！"
    log_info "接下來需要："
    log_info "1. 創建服務配置文件"
    log_info "2. 設置 systemd 服務"
    log_info "3. 啟動服務"
    log_info ""
    log_info "運行以下腳本繼續："
    log_info "./configure-services.sh"
}

# 執行主函數
main "$@"
