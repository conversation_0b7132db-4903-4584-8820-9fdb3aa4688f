# Shadowsocks 服務部署規劃

## 現有服務分析

### 現有 VPS 配置
- **Primary IP**: ************ (Admin)
  - HK-AD-SS: Port 28889, Password: x10udOVK5zwKf1lv
  - HK-TG-S5: Port 7777 (SOCKS5)

- **Secondary IP**: ************* (Clients)
  - HK-CS-SS: Port 30001, Password: *&WPFUVeeT*lmzKU
  - HK-RS-SS: Port 30002, Password: #4CXG7E^%0a*BgTB
  - HK-OP-SS: Port 30003, Password: SJ!Qdij4##JuBsQM
  - SOCKS5 服務: Ports 8001-8003

## 新 VPS 配置規劃 (***********)

### 端口分配策略
- **管理端口範圍**: 28000-28999 (與現有 Admin 保持一致)
- **客戶端口範圍**: 30000-30999 (與現有 Client 保持一致)
- **SOCKS5 端口範圍**: 8000-8999

### 建議的 SS 服務配置

#### 1. 管理員服務 (Admin)
- **服務名**: SG-AD-SS
- **端口**: 28890
- **加密**: aes-256-gcm
- **密碼**: K9mP2vX8nQ4wR7tY (新生成)
- **用途**: 管理員專用，高優先級

#### 2. 客戶服務組 (Clients)
- **服務名**: SG-CS-SS
- **端口**: 30004
- **加密**: aes-256-gcm
- **密碼**: L5nQ8xB3mV6zC9fG (新生成)
- **用途**: 普通客戶端

- **服務名**: SG-RS-SS
- **端口**: 30005
- **加密**: aes-256-gcm
- **密碼**: M7pR4yD9nK2sF8hJ (新生成)
- **用途**: 備用/負載均衡

- **服務名**: SG-OP-SS
- **端口**: 30006
- **加密**: aes-256-gcm
- **密碼**: N3tW6vE5mL8qA1xZ (新生成)
- **用途**: 特殊用途/測試

#### 3. SOCKS5 服務 (可選)
- **管理員 SOCKS5**: Port 7778
- **客戶端 SOCKS5**: Ports 8004-8006

### 服務器規格要求
- **CPU**: 1 核心以上
- **內存**: 512MB 以上
- **帶寬**: 10Mbps 以上
- **操作系統**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+

### 安全配置
- **防火牆**: 只開放必要端口
- **SSH**: 密鑰認證，禁用密碼登錄
- **監控**: 服務狀態監控和日誌記錄
- **自動重啟**: systemd 服務管理

### 部署步驟概覽
1. SSH 密鑰配置和連接測試
2. 安裝 go-shadowsocks2 和依賴
3. 創建服務配置文件
4. 設置 systemd 服務
5. 配置防火牆規則
6. 啟動和測試服務
7. 更新 Clash 配置文件
8. 性能測試和優化

### 預期性能
- **並發連接**: 1000+ 連接
- **吞吐量**: 100Mbps+ (取決於服務器帶寬)
- **延遲**: <50ms (區域內)
- **可用性**: 99.9%+
