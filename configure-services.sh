#!/bin/bash

# Shadowsocks 服務配置腳本
# 創建配置文件和啟動腳本

set -e

VPS_HOST="sg-vps"
CONFIG_DIR="/etc/shadowsocks"
INSTALL_DIR="/opt/shadowsocks"
LOG_DIR="/var/log/shadowsocks"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 創建服務配置文件
create_service_configs() {
    log_step "創建服務配置文件..."
    
    # 管理員服務配置
    cat > admin-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 28890,
    "password": "K9mP2vX8nQ4wR7tY",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

    # 客戶端服務配置 1
    cat > client1-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 30004,
    "password": "L5nQ8xB3mV6zC9fG",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

    # 備用服務配置
    cat > reserve-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 30005,
    "password": "M7pR4yD9nK2sF8hJ",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

    # 可選服務配置
    cat > optional-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 30006,
    "password": "N3tW6vE5mL8qA1xZ",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

    log_info "配置文件已創建"
}

# 上傳配置文件到服務器
upload_configs() {
    log_step "上傳配置文件到服務器..."
    
    scp admin-ss.json "$VPS_HOST:$CONFIG_DIR/"
    scp client1-ss.json "$VPS_HOST:$CONFIG_DIR/"
    scp reserve-ss.json "$VPS_HOST:$CONFIG_DIR/"
    scp optional-ss.json "$VPS_HOST:$CONFIG_DIR/"
    
    # 設置權限
    ssh "$VPS_HOST" "
        chown shadowsocks:shadowsocks $CONFIG_DIR/*.json
        chmod 600 $CONFIG_DIR/*.json
    "
    
    log_info "配置文件已上傳"
}

# 創建啟動腳本
create_start_scripts() {
    log_step "創建啟動腳本..."
    
    # 管理員服務啟動腳本
    cat > start-admin.sh << 'EOF'
#!/bin/bash
/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:K9mP2vX8nQ4wR7tY@:28890' -verbose
EOF

    # 客戶端服務啟動腳本
    cat > start-client1.sh << 'EOF'
#!/bin/bash
/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:L5nQ8xB3mV6zC9fG@:30004' -verbose
EOF

    cat > start-reserve.sh << 'EOF'
#!/bin/bash
/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:M7pR4yD9nK2sF8hJ@:30005' -verbose
EOF

    cat > start-optional.sh << 'EOF'
#!/bin/bash
/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:N3tW6vE5mL8qA1xZ@:30006' -verbose
EOF

    chmod +x start-*.sh
    
    # 上傳啟動腳本
    scp start-*.sh "$VPS_HOST:$INSTALL_DIR/"
    ssh "$VPS_HOST" "chmod +x $INSTALL_DIR/start-*.sh"
    
    log_info "啟動腳本已創建"
}

# 測試配置
test_configs() {
    log_step "測試配置..."
    
    ssh "$VPS_HOST" "
        echo '測試 go-shadowsocks2 安裝...'
        /usr/local/bin/shadowsocks2 -version || echo '版本信息不可用'
        
        echo '檢查配置文件...'
        ls -la $CONFIG_DIR/
        
        echo '檢查啟動腳本...'
        ls -la $INSTALL_DIR/start-*.sh
    "
    
    log_info "配置測試完成"
}

# 主函數
main() {
    log_info "開始配置 Shadowsocks 服務..."
    
    create_service_configs
    upload_configs
    create_start_scripts
    test_configs
    
    log_info "服務配置完成！"
    log_info "接下來運行: ./create-systemd-services.sh"
}

main "$@"
