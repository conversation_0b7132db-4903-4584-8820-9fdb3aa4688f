#!/bin/bash

# 啟動 Shadowsocks 服務腳本

set -e

VPS_HOST="sg-vps"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 啟動服務
start_services() {
    log_step "啟動 Shadowsocks 服務..."
    
    ssh "$VPS_HOST" "
        echo '啟動所有 Shadowsocks 服務...'
        systemctl start shadowsocks-admin.service
        systemctl start shadowsocks-client1.service
        systemctl start shadowsocks-reserve.service
        systemctl start shadowsocks-optional.service
        
        echo '等待服務啟動...'
        sleep 3
        
        echo '檢查服務狀態...'
        systemctl is-active shadowsocks-admin.service
        systemctl is-active shadowsocks-client1.service
        systemctl is-active shadowsocks-reserve.service
        systemctl is-active shadowsocks-optional.service
    "
    
    log_info "服務啟動完成"
}

# 檢查服務狀態
check_status() {
    log_step "檢查服務詳細狀態..."
    
    ssh "$VPS_HOST" "ss-manager.sh status"
}

# 測試端口
test_ports() {
    log_step "測試端口連通性..."
    
    local vps_ip="***********"
    local ports=(28890 30004 30005 30006)
    
    for port in "${ports[@]}"; do
        if timeout 5 bash -c "</dev/tcp/$vps_ip/$port" 2>/dev/null; then
            log_info "端口 $port: ✓ 連通"
        else
            log_warn "端口 $port: ✗ 無法連接"
        fi
    done
}

# 顯示日誌
show_logs() {
    log_step "顯示最近的服務日誌..."
    
    ssh "$VPS_HOST" "
        echo '=== Admin Service Log ==='
        tail -n 10 /var/log/shadowsocks/admin.log 2>/dev/null || echo '日誌文件不存在'
        echo ''
        echo '=== Client1 Service Log ==='
        tail -n 10 /var/log/shadowsocks/client1.log 2>/dev/null || echo '日誌文件不存在'
    "
}

# 主函數
main() {
    log_info "啟動 Shadowsocks 服務..."
    
    start_services
    echo ""
    
    check_status
    echo ""
    
    test_ports
    echo ""
    
    show_logs
    echo ""
    
    log_info "服務啟動完成！"
    log_info "使用 'ssh $VPS_HOST ss-manager.sh status' 查看詳細狀態"
    log_info "使用 'ssh $VPS_HOST ss-manager.sh logs [service]' 查看日誌"
}

main "$@"
