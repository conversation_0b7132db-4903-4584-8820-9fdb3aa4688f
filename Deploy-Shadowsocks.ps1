# Shadowsocks PowerShell 部署腳本
# 適用於 Windows 環境

param(
    [string]$VpsIp = "***********",
    [string]$VpsUser = "root",
    [int]$SshPort = 22
)

# 設置錯誤處理
$ErrorActionPreference = "Stop"

# 顏色輸出函數
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    switch ($Color) {
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        "Cyan" { Write-Host $Message -ForegroundColor Cyan }
        default { Write-Host $Message }
    }
}

function Write-Info { param([string]$Message) Write-ColorOutput "[INFO] $Message" "Green" }
function Write-Warn { param([string]$Message) Write-ColorOutput "[WARN] $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "[ERROR] $Message" "Red" }
function Write-Step { param([string]$Message) Write-ColorOutput "[STEP] $Message" "Blue" }

# 檢查 SSH 密鑰
function Test-SshKey {
    Write-Step "檢查 SSH 密鑰..."
    
    $sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa"
    
    if (-not (Test-Path $sshKeyPath)) {
        Write-Info "SSH 密鑰不存在，正在生成..."
        
        # 確保 .ssh 目錄存在
        $sshDir = "$env:USERPROFILE\.ssh"
        if (-not (Test-Path $sshDir)) {
            New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
        }
        
        # 生成 SSH 密鑰
        ssh-keygen -t rsa -b 4096 -f $sshKeyPath -N '""' -C "ss-deployment-$(Get-Date -Format 'yyyyMMdd')"
        
        Write-Info "SSH 密鑰已生成: $sshKeyPath"
    } else {
        Write-Info "SSH 密鑰已存在: $sshKeyPath"
    }
    
    # 顯示公鑰
    Write-Info "公鑰內容："
    Write-ColorOutput "$(Get-Content "$sshKeyPath.pub")" "Cyan"
    Write-Info "請將上述公鑰添加到 VPS 的 ~/.ssh/authorized_keys 文件中"
    
    return $sshKeyPath
}

# 測試 SSH 連接
function Test-SshConnection {
    param([string]$Host)
    
    Write-Step "測試 SSH 連接到 $Host..."
    
    try {
        $result = ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no $Host "echo 'SSH連接成功'"
        if ($result -eq "SSH連接成功") {
            Write-Info "SSH 連接正常"
            return $true
        }
    } catch {
        Write-Error "SSH 連接失敗: $_"
        return $false
    }
    
    return $false
}

# 在遠程服務器執行命令
function Invoke-RemoteCommand {
    param(
        [string]$Host,
        [string]$Command
    )
    
    try {
        $result = ssh $Host $Command
        return $result
    } catch {
        Write-Error "遠程命令執行失敗: $_"
        throw
    }
}

# 複製文件到遠程服務器
function Copy-ToRemote {
    param(
        [string]$LocalPath,
        [string]$Host,
        [string]$RemotePath
    )
    
    try {
        scp $LocalPath "${Host}:${RemotePath}"
        Write-Info "文件已複製: $LocalPath -> $RemotePath"
    } catch {
        Write-Error "文件複製失敗: $_"
        throw
    }
}

# 安裝 go-shadowsocks2
function Install-GoShadowsocks2 {
    param([string]$Host)
    
    Write-Step "安裝 go-shadowsocks2..."
    
    $installScript = @"
#!/bin/bash
set -e

# 更新系統
apt-get update

# 安裝依賴
apt-get install -y wget curl unzip systemd

# 創建服務用戶
if ! id shadowsocks &>/dev/null; then
    useradd -r -s /bin/false shadowsocks
    echo '創建服務用戶: shadowsocks'
fi

# 創建目錄
mkdir -p /opt/shadowsocks /etc/shadowsocks /var/log/shadowsocks
chown shadowsocks:shadowsocks /var/log/shadowsocks

# 檢測架構
ARCH=`$(uname -m)
case `$ARCH in
    x86_64) ARCH='linux-amd64' ;;
    aarch64) ARCH='linux-arm64' ;;
    armv7l) ARCH='linux-arm' ;;
    *) echo "不支持的架構: `$ARCH"; exit 1 ;;
esac

# 下載 go-shadowsocks2
cd /opt/shadowsocks
LATEST_URL=`$(curl -s https://api.github.com/repos/shadowsocks/go-shadowsocks2/releases/latest | grep "browser_download_url.*`$ARCH" | cut -d'"' -f4)

if [ -z "`$LATEST_URL" ]; then
    echo "無法獲取下載鏈接，使用備用方法..."
    wget -O shadowsocks2.gz "https://github.com/shadowsocks/go-shadowsocks2/releases/download/v0.1.5/shadowsocks2-linux.gz"
else
    wget -O shadowsocks2.gz "`$LATEST_URL"
fi

gunzip shadowsocks2.gz
chmod +x shadowsocks2
ln -sf /opt/shadowsocks/shadowsocks2 /usr/local/bin/shadowsocks2

echo "go-shadowsocks2 安裝完成"
"@
    
    # 創建臨時安裝腳本
    $tempScript = [System.IO.Path]::GetTempFileName() + ".sh"
    $installScript | Out-File -FilePath $tempScript -Encoding UTF8
    
    try {
        # 上傳並執行安裝腳本
        Copy-ToRemote $tempScript $Host "/tmp/install-ss.sh"
        Invoke-RemoteCommand $Host "chmod +x /tmp/install-ss.sh && /tmp/install-ss.sh"
        Invoke-RemoteCommand $Host "rm /tmp/install-ss.sh"
        
        Write-Info "go-shadowsocks2 安裝完成"
    } finally {
        Remove-Item $tempScript -Force -ErrorAction SilentlyContinue
    }
}

# 創建服務配置
function New-ServiceConfigs {
    param([string]$Host)
    
    Write-Step "創建服務配置..."
    
    # 創建配置文件
    $configs = @{
        "admin-ss.json" = @{
            server = "0.0.0.0"
            server_port = 28890
            password = "K9mP2vX8nQ4wR7tY"
            method = "aes-256-gcm"
            timeout = 300
            fast_open = $true
            nameserver = "*******"
            mode = "tcp_and_udp"
        }
        "client1-ss.json" = @{
            server = "0.0.0.0"
            server_port = 30004
            password = "L5nQ8xB3mV6zC9fG"
            method = "aes-256-gcm"
            timeout = 300
            fast_open = $true
            nameserver = "*******"
            mode = "tcp_and_udp"
        }
        "reserve-ss.json" = @{
            server = "0.0.0.0"
            server_port = 30005
            password = "M7pR4yD9nK2sF8hJ"
            method = "aes-256-gcm"
            timeout = 300
            fast_open = $true
            nameserver = "*******"
            mode = "tcp_and_udp"
        }
        "optional-ss.json" = @{
            server = "0.0.0.0"
            server_port = 30006
            password = "N3tW6vE5mL8qA1xZ"
            method = "aes-256-gcm"
            timeout = 300
            fast_open = $true
            nameserver = "*******"
            mode = "tcp_and_udp"
        }
    }
    
    foreach ($configName in $configs.Keys) {
        $configJson = $configs[$configName] | ConvertTo-Json -Depth 10
        $tempFile = [System.IO.Path]::GetTempFileName()
        $configJson | Out-File -FilePath $tempFile -Encoding UTF8
        
        Copy-ToRemote $tempFile $Host "/etc/shadowsocks/$configName"
        Remove-Item $tempFile -Force
    }
    
    # 設置權限
    Invoke-RemoteCommand $Host "chown shadowsocks:shadowsocks /etc/shadowsocks/*.json && chmod 600 /etc/shadowsocks/*.json"
    
    Write-Info "服務配置已創建"
}

# 主部署函數
function Start-Deployment {
    Write-Info "開始 Shadowsocks 部署..."
    Write-Info "VPS IP: $VpsIp"
    Write-Info "用戶: $VpsUser"
    
    # 設置 SSH 主機別名
    $sshHost = "$VpsUser@$VpsIp"
    
    try {
        # 步驟 1: 檢查 SSH 密鑰
        $sshKeyPath = Test-SshKey
        
        Write-Warn "請確保已將公鑰添加到 VPS 的 ~/.ssh/authorized_keys 文件中"
        $continue = Read-Host "是否已添加公鑰？(y/n)"
        if ($continue -ne 'y' -and $continue -ne 'Y') {
            Write-Info "請先添加公鑰，然後重新運行腳本"
            return
        }
        
        # 步驟 2: 測試 SSH 連接
        if (-not (Test-SshConnection $sshHost)) {
            Write-Error "SSH 連接失敗，請檢查網絡和密鑰配置"
            return
        }
        
        # 步驟 3: 安裝 go-shadowsocks2
        Install-GoShadowsocks2 $sshHost
        
        # 步驟 4: 創建服務配置
        New-ServiceConfigs $sshHost
        
        Write-Info "基礎部署完成！"
        Write-Info "接下來需要創建 systemd 服務並啟動"
        
    } catch {
        Write-Error "部署失敗: $_"
        throw
    }
}

# 執行部署
try {
    Start-Deployment
} catch {
    Write-Error "部署過程中發生錯誤: $($_.Exception.Message)"
    exit 1
}
