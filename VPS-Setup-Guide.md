# VPS 初始設置和 Shadowsocks 部署指南

## 🚨 當前狀況

**VPS IP**: ***********  
**狀態**: 網絡可達（ping 成功），但 SSH 服務無法連接

## 📋 問題診斷

我們已經測試了以下內容：
- ✅ VPS 網絡連通性（ping 成功，延遲 27-28ms）
- ❌ SSH 端口 22 無法連接
- ❌ 常見 SSH 端口（2222, 2200, 22022）無法連接

## 🔧 解決方案

### 方案 1: 通過 VPS 提供商控制面板

1. **登錄 VPS 提供商控制面板**
   - 查找 VNC/Console 訪問選項
   - 或者查找 "重置" 或 "重新安裝" 選項

2. **通過 VNC 連接到 VPS**
   ```bash
   # 檢查 SSH 服務狀態
   systemctl status ssh
   systemctl status sshd
   
   # 如果服務未運行，啟動它
   systemctl start ssh
   systemctl enable ssh
   
   # 檢查防火牆
   ufw status
   iptables -L
   
   # 如果需要，開放 SSH 端口
   ufw allow 22
   ```

3. **檢查 SSH 配置**
   ```bash
   # 查看 SSH 配置
   cat /etc/ssh/sshd_config
   
   # 確保以下設置正確
   Port 22
   PermitRootLogin yes
   PasswordAuthentication yes
   PubkeyAuthentication yes
   ```

### 方案 2: 重新安裝 VPS 系統

如果 VPS 系統有問題，建議重新安裝：

1. **選擇操作系統**: Ubuntu 20.04 LTS 或 Ubuntu 22.04 LTS
2. **設置 root 密碼**
3. **等待安裝完成**

### 方案 3: 檢查網絡配置

可能的網絡問題：
- VPS 提供商的防火牆設置
- 安全組配置
- 網絡 ACL 設置

## 🚀 一旦 SSH 可用，執行自動部署

當 SSH 連接可用後，運行以下命令：

### Windows 用戶
```cmd
# 測試 SSH 連接
ssh root@*********** "echo 'SSH 連接成功'"

# 如果連接成功，運行部署
deploy-all.bat
```

### 手動部署步驟

如果自動腳本有問題，可以手動執行：

```bash
# 1. 連接到 VPS
ssh root@***********

# 2. 更新系統
apt update && apt upgrade -y

# 3. 安裝依賴
apt install -y wget curl unzip systemd

# 4. 創建服務用戶
useradd -r -s /bin/false shadowsocks

# 5. 創建目錄
mkdir -p /opt/shadowsocks /etc/shadowsocks /var/log/shadowsocks
chown shadowsocks:shadowsocks /var/log/shadowsocks

# 6. 下載 go-shadowsocks2
cd /opt/shadowsocks
wget -O shadowsocks2.gz "https://github.com/shadowsocks/go-shadowsocks2/releases/download/v0.1.5/shadowsocks2-linux.gz"
gunzip shadowsocks2.gz
chmod +x shadowsocks2
ln -sf /opt/shadowsocks/shadowsocks2 /usr/local/bin/shadowsocks2

# 7. 測試安裝
shadowsocks2 -version
```

## 📝 服務配置

創建配置文件：

```bash
# 管理員服務配置
cat > /etc/shadowsocks/admin-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 28890,
    "password": "K9mP2vX8nQ4wR7tY",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

# 客戶端服務配置
cat > /etc/shadowsocks/client1-ss.json << 'EOF'
{
    "server": "0.0.0.0",
    "server_port": 30004,
    "password": "L5nQ8xB3mV6zC9fG",
    "method": "aes-256-gcm",
    "timeout": 300,
    "fast_open": true,
    "nameserver": "*******",
    "mode": "tcp_and_udp"
}
EOF

# 設置權限
chown shadowsocks:shadowsocks /etc/shadowsocks/*.json
chmod 600 /etc/shadowsocks/*.json
```

## 🔧 創建 systemd 服務

```bash
# 管理員服務
cat > /etc/systemd/system/shadowsocks-admin.service << 'EOF'
[Unit]
Description=Shadowsocks Admin Server
After=network.target

[Service]
Type=simple
User=shadowsocks
Group=shadowsocks
ExecStart=/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:K9mP2vX8nQ4wR7tY@:28890' -verbose
Restart=always
RestartSec=5
StandardOutput=append:/var/log/shadowsocks/admin.log
StandardError=append:/var/log/shadowsocks/admin.log

[Install]
WantedBy=multi-user.target
EOF

# 啟用並啟動服務
systemctl daemon-reload
systemctl enable shadowsocks-admin.service
systemctl start shadowsocks-admin.service
systemctl status shadowsocks-admin.service
```

## 🔥 配置防火牆

```bash
# 安裝 UFW（如果沒有）
apt install -y ufw

# 開放必要端口
ufw allow 22/tcp
ufw allow 28890/tcp
ufw allow 30004/tcp
ufw allow 30005/tcp
ufw allow 30006/tcp

# 啟用防火牆
ufw --force enable
ufw status
```

## 🧪 測試部署

```bash
# 檢查服務狀態
systemctl status shadowsocks-admin.service

# 檢查端口監聽
ss -tlnp | grep shadowsocks

# 測試連接
curl --socks5 127.0.0.1:28890 http://httpbin.org/ip
```

## 📞 需要幫助？

如果遇到問題：

1. **檢查 VPS 提供商文檔** - 查看 SSH 訪問說明
2. **聯繫 VPS 提供商支持** - 確認 SSH 服務狀態
3. **使用 VNC/Console** - 直接訪問 VPS 系統
4. **重新安裝系統** - 如果問題持續存在

## 📋 下一步

一旦 SSH 連接可用：
1. 運行自動部署腳本
2. 測試 Shadowsocks 服務
3. 更新 Clash 配置
4. 享受新的代理服務！
