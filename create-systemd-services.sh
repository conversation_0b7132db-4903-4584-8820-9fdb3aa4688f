#!/bin/bash

# 創建 systemd 服務文件腳本

set -e

VPS_HOST="sg-vps"
INSTALL_DIR="/opt/shadowsocks"
LOG_DIR="/var/log/shadowsocks"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 創建 systemd 服務文件
create_systemd_services() {
    log_step "創建 systemd 服務文件..."
    
    # 管理員服務
    cat > shadowsocks-admin.service << 'EOF'
[Unit]
Description=Shadowsocks Admin Server
After=network.target

[Service]
Type=simple
User=shadowsocks
Group=shadowsocks
ExecStart=/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:K9mP2vX8nQ4wR7tY@:28890' -verbose
Restart=always
RestartSec=5
StandardOutput=append:/var/log/shadowsocks/admin.log
StandardError=append:/var/log/shadowsocks/admin.log

[Install]
WantedBy=multi-user.target
EOF

    # 客戶端服務 1
    cat > shadowsocks-client1.service << 'EOF'
[Unit]
Description=Shadowsocks Client Server 1
After=network.target

[Service]
Type=simple
User=shadowsocks
Group=shadowsocks
ExecStart=/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:L5nQ8xB3mV6zC9fG@:30004' -verbose
Restart=always
RestartSec=5
StandardOutput=append:/var/log/shadowsocks/client1.log
StandardError=append:/var/log/shadowsocks/client1.log

[Install]
WantedBy=multi-user.target
EOF

    # 備用服務
    cat > shadowsocks-reserve.service << 'EOF'
[Unit]
Description=Shadowsocks Reserve Server
After=network.target

[Service]
Type=simple
User=shadowsocks
Group=shadowsocks
ExecStart=/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:M7pR4yD9nK2sF8hJ@:30005' -verbose
Restart=always
RestartSec=5
StandardOutput=append:/var/log/shadowsocks/reserve.log
StandardError=append:/var/log/shadowsocks/reserve.log

[Install]
WantedBy=multi-user.target
EOF

    # 可選服務
    cat > shadowsocks-optional.service << 'EOF'
[Unit]
Description=Shadowsocks Optional Server
After=network.target

[Service]
Type=simple
User=shadowsocks
Group=shadowsocks
ExecStart=/usr/local/bin/shadowsocks2 -s 'ss://aes-256-gcm:N3tW6vE5mL8qA1xZ@:30006' -verbose
Restart=always
RestartSec=5
StandardOutput=append:/var/log/shadowsocks/optional.log
StandardError=append:/var/log/shadowsocks/optional.log

[Install]
WantedBy=multi-user.target
EOF

    log_info "systemd 服務文件已創建"
}

# 上傳並安裝服務
install_services() {
    log_step "安裝 systemd 服務..."
    
    # 上傳服務文件
    scp shadowsocks-*.service "$VPS_HOST:/tmp/"
    
    # 安裝服務
    ssh "$VPS_HOST" "
        # 移動服務文件到正確位置
        mv /tmp/shadowsocks-*.service /etc/systemd/system/
        
        # 重新加載 systemd
        systemctl daemon-reload
        
        # 啟用服務（開機自啟）
        systemctl enable shadowsocks-admin.service
        systemctl enable shadowsocks-client1.service
        systemctl enable shadowsocks-reserve.service
        systemctl enable shadowsocks-optional.service
        
        echo 'systemd 服務已安裝並啟用'
    "
    
    log_info "服務安裝完成"
}

# 創建管理腳本
create_management_scripts() {
    log_step "創建管理腳本..."
    
    # 服務管理腳本
    cat > ss-manager.sh << 'EOF'
#!/bin/bash

# Shadowsocks 服務管理腳本

SERVICES=("shadowsocks-admin" "shadowsocks-client1" "shadowsocks-reserve" "shadowsocks-optional")

case "$1" in
    start)
        echo "啟動所有 Shadowsocks 服務..."
        for service in "${SERVICES[@]}"; do
            systemctl start "$service"
            echo "已啟動: $service"
        done
        ;;
    stop)
        echo "停止所有 Shadowsocks 服務..."
        for service in "${SERVICES[@]}"; do
            systemctl stop "$service"
            echo "已停止: $service"
        done
        ;;
    restart)
        echo "重啟所有 Shadowsocks 服務..."
        for service in "${SERVICES[@]}"; do
            systemctl restart "$service"
            echo "已重啟: $service"
        done
        ;;
    status)
        echo "檢查所有 Shadowsocks 服務狀態..."
        for service in "${SERVICES[@]}"; do
            echo "=== $service ==="
            systemctl status "$service" --no-pager -l
            echo ""
        done
        ;;
    logs)
        echo "查看服務日誌..."
        if [ -n "$2" ]; then
            journalctl -u "shadowsocks-$2" -f
        else
            echo "用法: $0 logs [admin|client1|reserve|optional]"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs [service]}"
        echo "服務列表: admin, client1, reserve, optional"
        exit 1
        ;;
esac
EOF

    chmod +x ss-manager.sh
    scp ss-manager.sh "$VPS_HOST:/usr/local/bin/"
    
    log_info "管理腳本已創建"
}

# 主函數
main() {
    log_info "開始創建 systemd 服務..."
    
    create_systemd_services
    install_services
    create_management_scripts
    
    log_info "systemd 服務創建完成！"
    log_info ""
    log_info "可用命令："
    log_info "  ssh $VPS_HOST ss-manager.sh start    # 啟動所有服務"
    log_info "  ssh $VPS_HOST ss-manager.sh status   # 檢查服務狀態"
    log_info "  ssh $VPS_HOST ss-manager.sh logs admin # 查看管理員服務日誌"
    log_info ""
    log_info "接下來運行: ./start-services.sh"
}

main "$@"
