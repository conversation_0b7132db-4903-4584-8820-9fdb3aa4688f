# Clash Configuration - Dual IP Multi-User Proxy (ChatGPT Fixed)
# Primary IP: ************ (Admin)
# Secondary IP: ************* (Clients)
# Generated: 2025-05-28
# Last Updated: 2025-07-02 - Added ChatGPT support

# Port Configuration
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893

# Allow LAN connections
allow-lan: true
bind-address: '*'

# Mode: rule / global / direct
mode: rule

# Log level: info / warning / error / debug / silent
log-level: info

# External controller
external-controller: 127.0.0.1:9090

# Proxy servers configuration
proxies:
  # Admin Shadowsocks - Primary IP
  - name: "HK-AD-SS"
    type: ss
    server: ************
    port: 28889
    cipher: aes-256-gcm
    password: x10udOVK5zwKf1lv
    udp: true

  # Client Shadowsocks - Secondary IP
  - name: "HK-CS-SS"
    type: ss
    server: *************
    port: 30001
    cipher: aes-256-gcm
    password: "*&WPFUVeeT*lmzKU"
    udp: true

  - name: "HK-RS-SS"
    type: ss
    server: *************
    port: 30002
    cipher: aes-256-gcm
    password: "#4CXG7E^%0a*BgTB"
    udp: true

  - name: "HK-OP-SS"
    type: ss
    server: *************
    port: 30003
    cipher: aes-256-gcm
    password: "SJ!Qdij4##JuBsQM"
    udp: true

  # Admin SOCKS5 - Primary IP
  - name: "HK-TG-S5"
    type: socks5
    server: ************
    port: 7777
    username: proxyuser
    password: MyProxy123!
    udp: true

  # Client SOCKS5 - Secondary IP
  - name: "HK-CS-S5"
    type: socks5
    server: *************
    port: 8001
    username: cs_user
    password: CSuser2025Pass
    udp: true

  - name: "HK-RS-S5"
    type: socks5
    server: *************
    port: 8002
    username: rs_user
    password: RSuser2025Pass
    udp: true

  - name: "HK-OP-S5"
    type: socks5
    server: *************
    port: 8003
    username: op_user
    password: OPuser2025Pass
    udp: true

  # Singapore VPS Shadowsocks - New Server (***********)
  - name: "SG-AD-SS"
    type: ss
    server: ***********
    port: 28890
    cipher: aes-256-gcm
    password: K9mP2vX8nQ4wR7tY
    udp: true

  - name: "SG-CS-SS"
    type: ss
    server: ***********
    port: 30004
    cipher: aes-256-gcm
    password: L5nQ8xB3mV6zC9fG
    udp: true

  - name: "SG-RS-SS"
    type: ss
    server: ***********
    port: 30005
    cipher: aes-256-gcm
    password: M7pR4yD9nK2sF8hJ
    udp: true

  - name: "SG-OP-SS"
    type: ss
    server: ***********
    port: 30006
    cipher: aes-256-gcm
    password: N3tW6vE5mL8qA1xZ
    udp: true

# Proxy groups configuration
proxy-groups:
  # Main selection group
  - name: "Proxy"
    type: select
    proxies:
      - "Auto"
      - "Admin"
      - "Client"
      - "Singapore"
      - "HK-AD-SS"
      - "HK-CS-SS"
      - "HK-RS-SS"
      - "HK-OP-SS"
      - "HK-TG-S5"
      - "HK-CS-S5"
      - "HK-RS-S5"
      - "HK-OP-S5"
      - "SG-AD-SS"
      - "SG-CS-SS"
      - "SG-RS-SS"
      - "SG-OP-SS"
      - "DIRECT"

  # Auto selection group - Based on latency
  - name: "Auto"
    type: url-test
    proxies:
      - "HK-AD-SS"
      - "HK-CS-SS"
      - "HK-RS-SS"
      - "HK-OP-SS"
      - "HK-TG-S5"
      - "HK-CS-S5"
      - "HK-RS-S5"
      - "HK-OP-S5"
      - "SG-AD-SS"
      - "SG-CS-SS"
      - "SG-RS-SS"
      - "SG-OP-SS"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # Admin group - Primary IP only
  - name: "Admin"
    type: select
    proxies:
      - "HK-AD-SS"
      - "HK-TG-S5"

  # Client group - Secondary IP only
  - name: "Client"
    type: select
    proxies:
      - "HK-CS-SS"
      - "HK-RS-SS"
      - "HK-OP-SS"
      - "HK-CS-S5"
      - "HK-RS-S5"
      - "HK-OP-S5"

  # Singapore group - New VPS
  - name: "Singapore"
    type: select
    proxies:
      - "SG-AD-SS"
      - "SG-CS-SS"
      - "SG-RS-SS"
      - "SG-OP-SS"

  # ChatGPT and AI services - Use direct or specific proxy
  - name: "ChatGPT"
    type: select
    proxies:
      - "DIRECT"
      - "Admin"
      - "Singapore"
      - "HK-AD-SS"
      - "HK-TG-S5"
      - "SG-AD-SS"

  # International media
  - name: "Media"
    type: select
    proxies:
      - "Proxy"
      - "Auto"
      - "Admin"
      - "Client"
      - "Singapore"

  # Social media
  - name: "Social"
    type: select
    proxies:
      - "Proxy"
      - "Auto"
      - "Admin"
      - "Client"
      - "Singapore"

  # Global acceleration
  - name: "Global"
    type: select
    proxies:
      - "Proxy"
      - "Auto"
      - "Admin"
      - "Client"
      - "Singapore"
      - "DIRECT"

  # Final catch-all
  - name: "Others"
    type: select
    proxies:
      - "Proxy"
      - "DIRECT"

# Rules configuration
rules:
  # Local network direct connection
  - DOMAIN-SUFFIX,local,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT

  # ChatGPT and OpenAI services - Use ChatGPT group (DIRECT or Admin proxy)
  - DOMAIN-SUFFIX,openai.com,ChatGPT
  - DOMAIN-SUFFIX,chatgpt.com,ChatGPT
  - DOMAIN-SUFFIX,oaistatic.com,ChatGPT
  - DOMAIN-SUFFIX,oaiusercontent.com,ChatGPT
  - DOMAIN-SUFFIX,openaiapi-site.azureedge.net,ChatGPT
  - DOMAIN-KEYWORD,openai,ChatGPT
  - DOMAIN-KEYWORD,chatgpt,ChatGPT

  # Other AI services that might be blocked in HK
  - DOMAIN-SUFFIX,anthropic.com,ChatGPT
  - DOMAIN-SUFFIX,claude.ai,ChatGPT
  - DOMAIN-SUFFIX,bard.google.com,ChatGPT
  - DOMAIN-SUFFIX,gemini.google.com,ChatGPT

  # Social media platforms
  - DOMAIN-SUFFIX,facebook.com,Social
  - DOMAIN-SUFFIX,instagram.com,Social
  - DOMAIN-SUFFIX,twitter.com,Social
  - DOMAIN-SUFFIX,x.com,Social
  - DOMAIN-SUFFIX,telegram.org,Social
  - DOMAIN-SUFFIX,whatsapp.com,Social
  - DOMAIN-SUFFIX,discord.com,Social
  - DOMAIN-SUFFIX,reddit.com,Social

  # International media services
  - DOMAIN-SUFFIX,youtube.com,Media
  - DOMAIN-SUFFIX,youtu.be,Media
  - DOMAIN-SUFFIX,googlevideo.com,Media
  - DOMAIN-SUFFIX,netflix.com,Media
  - DOMAIN-SUFFIX,nflxvideo.net,Media
  - DOMAIN-SUFFIX,twitch.tv,Media
  - DOMAIN-SUFFIX,hulu.com,Media
  - DOMAIN-SUFFIX,disney.com,Media
  - DOMAIN-SUFFIX,disneyplus.com,Media

  # Google services
  - DOMAIN-SUFFIX,google.com,Global
  - DOMAIN-SUFFIX,googleapis.com,Global
  - DOMAIN-SUFFIX,googleusercontent.com,Global
  - DOMAIN-SUFFIX,gstatic.com,Global
  - DOMAIN-SUFFIX,gmail.com,Global
  - DOMAIN-SUFFIX,googlemail.com,Global

  # GitHub
  - DOMAIN-SUFFIX,github.com,Global
  - DOMAIN-SUFFIX,githubusercontent.com,Global
  - DOMAIN-SUFFIX,githubassets.com,Global

  # Other common international services
  - DOMAIN-SUFFIX,cloudflare.com,Global
  - DOMAIN-SUFFIX,dropbox.com,Global
  - DOMAIN-SUFFIX,onedrive.com,Global
  - DOMAIN-SUFFIX,microsoft.com,Global
  - DOMAIN-SUFFIX,office.com,Global
  - DOMAIN-SUFFIX,zoom.us,Global

  # Visual Studio Code - Direct connection to avoid proxy issues
  - DOMAIN-SUFFIX,vscode.dev,DIRECT
  - DOMAIN-SUFFIX,visualstudio.com,DIRECT
  - DOMAIN-SUFFIX,vscode-cdn.net,DIRECT
  - DOMAIN-SUFFIX,vscode-unpkg.net,DIRECT
  - DOMAIN-SUFFIX,vscode-webview.net,DIRECT
  - DOMAIN-SUFFIX,vscodeexperiments.azureedge.net,DIRECT
  - DOMAIN-SUFFIX,vsassets.io,DIRECT
  - DOMAIN-SUFFIX,vscode-sync.trafficmanager.net,DIRECT
  - DOMAIN-SUFFIX,marketplace.visualstudio.com,DIRECT
  - DOMAIN-SUFFIX,gallery.vsassets.io,DIRECT
  - DOMAIN-SUFFIX,vscode-update.azurewebsites.net,DIRECT
  - DOMAIN-SUFFIX,az764295.vo.msecnd.net,DIRECT
  - DOMAIN-SUFFIX,vscode-extensions.azureedge.net,DIRECT
  - DOMAIN-KEYWORD,vscode,DIRECT
  - DOMAIN-KEYWORD,visualstudio,DIRECT

  # China mainland websites direct connection
  - DOMAIN-SUFFIX,cn,DIRECT
  - DOMAIN-KEYWORD,-cn,DIRECT
  - DOMAIN-SUFFIX,baidu.com,DIRECT
  - DOMAIN-SUFFIX,qq.com,DIRECT
  - DOMAIN-SUFFIX,taobao.com,DIRECT
  - DOMAIN-SUFFIX,tmall.com,DIRECT
  - DOMAIN-SUFFIX,jd.com,DIRECT
  - DOMAIN-SUFFIX,weibo.com,DIRECT
  - DOMAIN-SUFFIX,zhihu.com,DIRECT
  - DOMAIN-SUFFIX,bilibili.com,DIRECT
  - DOMAIN-SUFFIX,douyin.com,DIRECT
  - DOMAIN-SUFFIX,tiktok.com,DIRECT

  # China mainland IP direct connection
  - GEOIP,CN,DIRECT

  # Final rule
  - MATCH,Others

# DNS configuration
dns:
  enable: true
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *********
    - ************
    - *******
  fallback:
    - *******
    - *******
    - tls://dns.google
  fallback-filter:
    geoip: true
    ipcidr:
      - 240.0.0.0/4
