# Shadowsocks 服務部署指南

本項目提供了在新 VPS (***********) 上自動部署 go-shadowsocks2 服務的完整解決方案。

## 📋 部署概覽

### 新增服務配置
- **服務器 IP**: ***********
- **實現**: go-shadowsocks2
- **服務數量**: 4 個 SS 服務實例
- **管理方式**: systemd 服務管理

### 服務列表
| 服務名稱 | 端口 | 密碼 | 用途 |
|---------|------|------|------|
| SG-AD-SS | 28890 | K9mP2vX8nQ4wR7tY | 管理員專用 |
| SG-CS-SS | 30004 | L5nQ8xB3mV6zC9fG | 普通客戶端 |
| SG-RS-SS | 30005 | M7pR4yD9nK2sF8hJ | 備用服務 |
| SG-OP-SS | 30006 | N3tW6vE5mL8qA1xZ | 可選服務 |

## 🚀 快速部署

### 一鍵部署
```bash
chmod +x deploy-all.sh
./deploy-all.sh
```

### 分步部署
```bash
# 1. 設置 SSH 密鑰
./ssh-setup.sh

# 2. 部署 go-shadowsocks2
./deploy-shadowsocks.sh

# 3. 配置服務
./configure-services.sh

# 4. 創建 systemd 服務
./create-systemd-services.sh

# 5. 啟動服務
./start-services.sh
```

## 📁 文件說明

### 部署腳本
- `deploy-all.sh` - 一鍵部署腳本
- `ssh-setup.sh` - SSH 密鑰設置
- `deploy-shadowsocks.sh` - 安裝 go-shadowsocks2
- `configure-services.sh` - 創建服務配置
- `create-systemd-services.sh` - 創建 systemd 服務
- `start-services.sh` - 啟動服務

### 配置文件
- `ss-configs.json` - 服務配置參數
- `ssh-config` - SSH 連接配置
- `clash-config-chatgpt-fixed.yaml` - 更新後的 Clash 配置

### 文檔
- `ss-deployment-plan.md` - 詳細部署規劃
- `README.md` - 本文件

## 🔧 服務管理

### 基本命令
```bash
# 連接到 VPS
ssh sg-vps

# 啟動所有服務
ss-manager.sh start

# 停止所有服務
ss-manager.sh stop

# 重啟所有服務
ss-manager.sh restart

# 查看服務狀態
ss-manager.sh status

# 查看特定服務日誌
ss-manager.sh logs admin
ss-manager.sh logs client1
ss-manager.sh logs reserve
ss-manager.sh logs optional
```

### 單個服務管理
```bash
# 管理特定服務
systemctl start shadowsocks-admin.service
systemctl stop shadowsocks-client1.service
systemctl restart shadowsocks-reserve.service
systemctl status shadowsocks-optional.service
```

## 📊 監控和日誌

### 日誌位置
- 管理員服務: `/var/log/shadowsocks/admin.log`
- 客戶端服務 1: `/var/log/shadowsocks/client1.log`
- 備用服務: `/var/log/shadowsocks/reserve.log`
- 可選服務: `/var/log/shadowsocks/optional.log`

### 實時監控
```bash
# 查看實時日誌
journalctl -u shadowsocks-admin.service -f

# 查看所有 SS 服務狀態
systemctl status shadowsocks-*.service
```

## 🌐 Clash 配置更新

已自動更新 `clash-config-chatgpt-fixed.yaml`，新增：
- 4 個新的 SS 代理配置
- Singapore 代理組
- 在各個代理組中添加新服務

### 新增代理組
- **Singapore**: 包含所有新加坡 VPS 的 SS 服務
- 已將 Singapore 組添加到 Media、Social、Global 等組中

## 🔒 安全配置

### 防火牆規則
- 只開放必要的 SS 端口 (28890, 30004-30006)
- 支持 TCP 和 UDP 協議
- SSH 密鑰認證，禁用密碼登錄

### 服務安全
- 使用專用 `shadowsocks` 用戶運行服務
- 配置文件權限限制 (600)
- 自動重啟機制

## 🛠️ 故障排除

### 常見問題
1. **SSH 連接失敗**
   - 檢查 SSH 密鑰是否正確配置
   - 確認 VPS IP 地址和端口

2. **服務啟動失敗**
   - 檢查端口是否被占用: `netstat -tlnp | grep :28890`
   - 查看服務日誌: `journalctl -u shadowsocks-admin.service`

3. **連接測試失敗**
   - 檢查防火牆設置: `ufw status`
   - 確認服務正在運行: `systemctl status shadowsocks-*.service`

### 調試命令
```bash
# 檢查端口監聽
ss -tlnp | grep shadowsocks

# 測試本地連接
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip

# 檢查進程
ps aux | grep shadowsocks
```

## 📈 性能優化

### 系統優化
- 調整文件描述符限制
- 優化網絡參數
- 啟用 TCP Fast Open

### 監控指標
- 連接數量
- 流量統計
- 延遲測試
- 可用性監控

## 🔄 維護和更新

### 定期維護
```bash
# 更新 go-shadowsocks2
./deploy-shadowsocks.sh

# 重新生成密碼
./configure-services.sh

# 備份配置
tar -czf ss-backup-$(date +%Y%m%d).tar.gz /etc/shadowsocks/
```

### 版本更新
定期檢查 go-shadowsocks2 的新版本並更新。

---

## 📞 支持

如有問題，請檢查：
1. 部署日誌
2. 服務狀態
3. 網絡連通性
4. 防火牆配置
