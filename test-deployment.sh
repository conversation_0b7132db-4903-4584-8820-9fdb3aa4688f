#!/bin/bash

# Shadowsocks 部署測試腳本

set -e

VPS_HOST="sg-vps"
VPS_IP="***********"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[TEST]${NC} $1"; }

# 測試 SSH 連接
test_ssh() {
    log_step "測試 SSH 連接..."
    
    if ssh -o ConnectTimeout=10 "$VPS_HOST" "echo 'SSH 連接正常'" > /dev/null 2>&1; then
        log_info "✓ SSH 連接成功"
        return 0
    else
        log_error "✗ SSH 連接失敗"
        return 1
    fi
}

# 測試 go-shadowsocks2 安裝
test_shadowsocks_install() {
    log_step "測試 go-shadowsocks2 安裝..."
    
    if ssh "$VPS_HOST" "which shadowsocks2" > /dev/null 2>&1; then
        log_info "✓ go-shadowsocks2 已安裝"
        ssh "$VPS_HOST" "shadowsocks2 -version 2>/dev/null || echo 'go-shadowsocks2 已安裝'"
        return 0
    else
        log_error "✗ go-shadowsocks2 未安裝"
        return 1
    fi
}

# 測試配置文件
test_config_files() {
    log_step "測試配置文件..."
    
    local configs=("admin-ss.json" "client1-ss.json" "reserve-ss.json" "optional-ss.json")
    local all_exist=true
    
    for config in "${configs[@]}"; do
        if ssh "$VPS_HOST" "test -f /etc/shadowsocks/$config"; then
            log_info "✓ 配置文件存在: $config"
        else
            log_error "✗ 配置文件缺失: $config"
            all_exist=false
        fi
    done
    
    return $all_exist
}

# 測試 systemd 服務
test_systemd_services() {
    log_step "測試 systemd 服務..."
    
    local services=("shadowsocks-admin" "shadowsocks-client1" "shadowsocks-reserve" "shadowsocks-optional")
    local all_enabled=true
    
    for service in "${services[@]}"; do
        if ssh "$VPS_HOST" "systemctl is-enabled $service.service" > /dev/null 2>&1; then
            log_info "✓ 服務已啟用: $service"
        else
            log_error "✗ 服務未啟用: $service"
            all_enabled=false
        fi
    done
    
    return $all_enabled
}

# 測試服務運行狀態
test_service_status() {
    log_step "測試服務運行狀態..."
    
    local services=("shadowsocks-admin" "shadowsocks-client1" "shadowsocks-reserve" "shadowsocks-optional")
    local all_running=true
    
    for service in "${services[@]}"; do
        if ssh "$VPS_HOST" "systemctl is-active $service.service" > /dev/null 2>&1; then
            log_info "✓ 服務正在運行: $service"
        else
            log_warn "⚠ 服務未運行: $service"
            all_running=false
        fi
    done
    
    return $all_running
}

# 測試端口連通性
test_port_connectivity() {
    log_step "測試端口連通性..."
    
    local ports=(28890 30004 30005 30006)
    local all_open=true
    
    for port in "${ports[@]}"; do
        if timeout 5 bash -c "</dev/tcp/$VPS_IP/$port" 2>/dev/null; then
            log_info "✓ 端口 $port 可連接"
        else
            log_warn "⚠ 端口 $port 無法連接"
            all_open=false
        fi
    done
    
    return $all_open
}

# 測試管理腳本
test_management_script() {
    log_step "測試管理腳本..."
    
    if ssh "$VPS_HOST" "test -f /usr/local/bin/ss-manager.sh"; then
        log_info "✓ 管理腳本已安裝"
        if ssh "$VPS_HOST" "test -x /usr/local/bin/ss-manager.sh"; then
            log_info "✓ 管理腳本可執行"
            return 0
        else
            log_error "✗ 管理腳本不可執行"
            return 1
        fi
    else
        log_error "✗ 管理腳本未安裝"
        return 1
    fi
}

# 測試日誌文件
test_log_files() {
    log_step "測試日誌文件..."
    
    local logs=("admin.log" "client1.log" "reserve.log" "optional.log")
    local all_exist=true
    
    for log in "${logs[@]}"; do
        if ssh "$VPS_HOST" "test -f /var/log/shadowsocks/$log"; then
            log_info "✓ 日誌文件存在: $log"
        else
            log_warn "⚠ 日誌文件不存在: $log (服務可能未啟動)"
        fi
    done
    
    return 0
}

# 生成測試報告
generate_report() {
    log_step "生成測試報告..."
    
    echo ""
    echo "=== Shadowsocks 部署測試報告 ==="
    echo "測試時間: $(date)"
    echo "VPS IP: $VPS_IP"
    echo ""
    
    # 獲取系統信息
    echo "=== 系統信息 ==="
    ssh "$VPS_HOST" "
        echo '操作系統: '$(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')
        echo 'CPU 核心: '$(nproc)
        echo '內存使用: '$(free -h | grep '^Mem:' | awk '{print \$3\"/\"\$2}')
        echo '磁盤使用: '$(df -h / | tail -1 | awk '{print \$3\"/\"\$2\" (\"\$5\")\"}')
    "
    
    echo ""
    echo "=== 服務狀態 ==="
    ssh "$VPS_HOST" "ss-manager.sh status" 2>/dev/null || echo "管理腳本不可用"
    
    echo ""
    echo "=== 網絡監聽 ==="
    ssh "$VPS_HOST" "ss -tlnp | grep shadowsocks || netstat -tlnp | grep shadowsocks || echo '無 shadowsocks 進程監聽'"
    
    echo ""
    echo "=== 防火牆狀態 ==="
    ssh "$VPS_HOST" "ufw status 2>/dev/null || echo 'UFW 未安裝或未啟用'"
    
    echo ""
    log_info "測試報告生成完成"
}

# 主函數
main() {
    log_info "開始 Shadowsocks 部署測試..."
    echo ""
    
    local test_results=()
    
    # 執行所有測試
    test_ssh && test_results+=("SSH連接: ✓") || test_results+=("SSH連接: ✗")
    test_shadowsocks_install && test_results+=("SS安裝: ✓") || test_results+=("SS安裝: ✗")
    test_config_files && test_results+=("配置文件: ✓") || test_results+=("配置文件: ✗")
    test_systemd_services && test_results+=("系統服務: ✓") || test_results+=("系統服務: ✗")
    test_service_status && test_results+=("服務狀態: ✓") || test_results+=("服務狀態: ⚠")
    test_port_connectivity && test_results+=("端口連通: ✓") || test_results+=("端口連通: ⚠")
    test_management_script && test_results+=("管理腳本: ✓") || test_results+=("管理腳本: ✗")
    test_log_files && test_results+=("日誌文件: ✓") || test_results+=("日誌文件: ⚠")
    
    echo ""
    echo "=== 測試結果摘要 ==="
    for result in "${test_results[@]}"; do
        echo "$result"
    done
    
    echo ""
    generate_report
    
    echo ""
    log_info "測試完成！"
    log_info "如果有 ✗ 標記的項目，請檢查相應的部署步驟"
    log_info "如果有 ⚠ 標記的項目，可能需要手動啟動服務"
}

main "$@"
