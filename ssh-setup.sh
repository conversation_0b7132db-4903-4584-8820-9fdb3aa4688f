#!/bin/bash

# SSH 密鑰設置腳本
# 用於配置到新 VPS 的 SSH 連接

set -e

VPS_IP="***********"
VPS_USER="root"
SSH_KEY_PATH="$HOME/.ssh/id_rsa"
SSH_CONFIG_PATH="$HOME/.ssh/config"

echo "=== SSH 密鑰設置腳本 ==="
echo "VPS IP: $VPS_IP"
echo "用戶: $VPS_USER"

# 檢查 SSH 密鑰是否存在
if [ ! -f "$SSH_KEY_PATH" ]; then
    echo "SSH 密鑰不存在，正在生成新密鑰..."
    ssh-keygen -t rsa -b 4096 -f "$SSH_KEY_PATH" -N "" -C "ss-deployment-$(date +%Y%m%d)"
    echo "SSH 密鑰已生成: $SSH_KEY_PATH"
else
    echo "SSH 密鑰已存在: $SSH_KEY_PATH"
fi

# 設置正確的權限
chmod 600 "$SSH_KEY_PATH"
chmod 644 "$SSH_KEY_PATH.pub"

# 顯示公鑰
echo ""
echo "=== 公鑰內容 ==="
echo "請將以下公鑰添加到 VPS 的 ~/.ssh/authorized_keys 文件中："
echo ""
cat "$SSH_KEY_PATH.pub"
echo ""

# 添加 SSH 配置
if [ -f "$SSH_CONFIG_PATH" ]; then
    # 檢查是否已存在配置
    if grep -q "Host sg-vps" "$SSH_CONFIG_PATH"; then
        echo "SSH 配置已存在於 $SSH_CONFIG_PATH"
    else
        echo "正在添加 SSH 配置到 $SSH_CONFIG_PATH..."
        cat >> "$SSH_CONFIG_PATH" << EOF

# Shadowsocks VPS Configuration
Host sg-vps
    HostName $VPS_IP
    User $VPS_USER
    Port 22
    IdentityFile $SSH_KEY_PATH
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    StrictHostKeyChecking ask
EOF
        echo "SSH 配置已添加"
    fi
else
    echo "正在創建 SSH 配置文件..."
    mkdir -p "$(dirname "$SSH_CONFIG_PATH")"
    cat > "$SSH_CONFIG_PATH" << EOF
# Shadowsocks VPS Configuration
Host sg-vps
    HostName $VPS_IP
    User $VPS_USER
    Port 22
    IdentityFile $SSH_KEY_PATH
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    StrictHostKeyChecking ask
EOF
    chmod 600 "$SSH_CONFIG_PATH"
    echo "SSH 配置文件已創建"
fi

echo ""
echo "=== 手動步驟 ==="
echo "1. 將上面的公鑰複製到 VPS"
echo "2. 在 VPS 上執行以下命令："
echo "   mkdir -p ~/.ssh"
echo "   echo '公鑰內容' >> ~/.ssh/authorized_keys"
echo "   chmod 700 ~/.ssh"
echo "   chmod 600 ~/.ssh/authorized_keys"
echo ""
echo "3. 測試連接："
echo "   ssh sg-vps"
echo ""
echo "=== 自動化選項 ==="
echo "如果您有 VPS 的密碼，可以運行："
echo "   ssh-copy-id -i $SSH_KEY_PATH.pub $VPS_USER@$VPS_IP"
echo ""

# 提供測試連接的選項
read -p "是否現在測試 SSH 連接？ (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在測試 SSH 連接..."
    ssh -o ConnectTimeout=10 sg-vps "echo 'SSH 連接成功！'; uname -a"
fi

echo "SSH 設置完成！"
