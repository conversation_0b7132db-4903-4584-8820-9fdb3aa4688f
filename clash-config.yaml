# Clash Configuration for Shadowsocks VPS
# Server: ************* (srv461468751.host)
# Generated: 2025-07-09

port: 7890
socks-port: 7891
allow-lan: false
mode: rule
log-level: info
external-controller: 127.0.0.1:9090
secret: ""

dns:
  enable: true
  ipv6: false
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *******
    - *******
    - *******
    - *******
  fallback:
    - tls://dns.google
    - tls://*******
    - https://dns.google/dns-query
    - https://*******/dns-query

proxies:
  - name: "SS-Group1"
    type: ss
    server: *************
    port: 8388
    cipher: chacha20-ietf-poly1305
    password: "Group1Pass2025"
    udp: true

  - name: "SS-Group2"
    type: ss
    server: *************
    port: 8389
    cipher: chacha20-ietf-poly1305
    password: "Group2Pass2025"
    udp: true

  - name: "SS-Group3"
    type: ss
    server: *************
    port: 8390
    cipher: chacha20-ietf-poly1305
    password: "Group3Pass2025"
    udp: true

  - name: "SS-Group4"
    type: ss
    server: *************
    port: 8391
    cipher: chacha20-ietf-poly1305
    password: "Group4Pass2025"
    udp: true

proxy-groups:
  - name: "Proxy"
    type: select
    proxies:
      - "Auto-Select"
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
      - "DIRECT"

  - name: "Auto-Select"
    type: url-test
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  - name: "Fallback"
    type: fallback
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  - name: "Load-Balance"
    type: load-balance
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    strategy: consistent-hashing

  - name: "Streaming"
    type: select
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
      - "Auto-Select"

  - name: "AdBlock"
    type: select
    proxies:
      - "REJECT"
      - "DIRECT"

rules:
  # Local Area Network
  - DOMAIN-SUFFIX,local,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT

  # Ad Blocking
  - DOMAIN-SUFFIX,doubleclick.net,AdBlock
  - DOMAIN-SUFFIX,googleadservices.com,AdBlock
  - DOMAIN-SUFFIX,googlesyndication.com,AdBlock
  - DOMAIN-SUFFIX,googletagservices.com,AdBlock
  - DOMAIN-SUFFIX,facebook.com,AdBlock
  - DOMAIN-SUFFIX,fbcdn.net,AdBlock

  # Streaming Services
  - DOMAIN-SUFFIX,netflix.com,Streaming
  - DOMAIN-SUFFIX,youtube.com,Streaming
  - DOMAIN-SUFFIX,googlevideo.com,Streaming
  - DOMAIN-SUFFIX,ytimg.com,Streaming
  - DOMAIN-SUFFIX,twitch.tv,Streaming
  - DOMAIN-SUFFIX,hulu.com,Streaming
  - DOMAIN-SUFFIX,disney.com,Streaming
  - DOMAIN-SUFFIX,disneyplus.com,Streaming

  # Global Services
  - DOMAIN-SUFFIX,google.com,Proxy
  - DOMAIN-SUFFIX,googleapis.com,Proxy
  - DOMAIN-SUFFIX,gstatic.com,Proxy
  - DOMAIN-SUFFIX,twitter.com,Proxy
  - DOMAIN-SUFFIX,instagram.com,Proxy
  - DOMAIN-SUFFIX,telegram.org,Proxy
  - DOMAIN-SUFFIX,github.com,Proxy
  - DOMAIN-SUFFIX,githubusercontent.com,Proxy

  # China Direct
  - DOMAIN-SUFFIX,cn,DIRECT
  - DOMAIN-KEYWORD,baidu,DIRECT
  - DOMAIN-KEYWORD,taobao,DIRECT
  - DOMAIN-KEYWORD,alipay,DIRECT
  - DOMAIN-KEYWORD,wechat,DIRECT
  - DOMAIN-KEYWORD,qq,DIRECT

  # Final Rule
  - GEOIP,CN,DIRECT
  - MATCH,Proxy
